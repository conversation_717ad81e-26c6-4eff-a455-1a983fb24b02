package com.arrivinginhighheels.visited.backend.features.iap;

import com.apple.itunes.storekit.client.APIException;
import com.apple.itunes.storekit.model.NotificationTypeV2;
import com.apple.itunes.storekit.model.Subtype;
import com.apple.itunes.storekit.verification.VerificationException;
import com.arrivinginhighheels.visited.backend.config.YamlConfig;
import com.arrivinginhighheels.visited.backend.dto.IAPPurchaseValidationDTO;
import com.arrivinginhighheels.visited.backend.dto.IAPReceiptDTO;
import com.arrivinginhighheels.visited.backend.model.OSType;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.repository.UserRepository;
import lombok.SneakyThrows;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class UserPurchasesService {
    private final UserPurchasesRepository userPurchasesRepository;
    private final UserRepository userRepository;
    private final AppStoreConnectService appStoreConnectService;
    private final PlayStoreClient googlePlayService;
    private final YamlConfig yamlConfig;

    public UserPurchasesService(
            AppStoreConnectService appStoreConnectService,
            UserPurchasesRepository userPurchasesRepository,
            UserRepository userRepository,
            PlayStoreClient googlePlayService,
            YamlConfig yamlConfig
    ) {
        this.appStoreConnectService = appStoreConnectService;
        this.userPurchasesRepository = userPurchasesRepository;
        this.userRepository = userRepository;
        this.googlePlayService = googlePlayService;
        this.yamlConfig = yamlConfig;
    }

    /// Current Production version method
//    public IAPPurchaseValidationDTO validate(
//            IAPReceiptDTO receipt,
//            OSType platform,
//            User user
//    ) {
//        final var productId = receipt.getProductId();
//        final var receiptData = receipt.getToken();
//
//        Optional<ValidatedPurchase> maybeValidatedPurchase = Optional.empty();
//        if (receiptData != null && platform.equals(OSType.iOS)) {
//            try {
//                maybeValidatedPurchase = appStoreConnectService.validateAppReceiptAndExtractTransactionId(productId, receiptData);
//            } catch (Exception e) {
//                Sentry.addBreadcrumb("Apple Validation Failed:" + productId + "token: " + receiptData);
//                Sentry.captureException(e);
//
//                // Hack together a quick ValidatedPurchase if apple validation fails.
//                // Validations are not perfect at the moment because of the migration to StoreKit2
//                // Non-consumables in particular can (but not always) fail server side validation
//                // TODO: Migrate to API based validation since the apple server library can product errors
//                var productType = productId.contains("subscription") ? UserPurchaseProductType.AUTO_RENEWABLE : UserPurchaseProductType.NON_CONSUMABLE;
//
//                maybeValidatedPurchase = Optional.of(new ValidatedPurchase(
//                        productId,
//                        receipt.getPurchaseId(),
//                        UserPurchaseStatus.ACTIVE,
//                        productType,
//                        Optional.empty(),
//                        false
//                ));
//            }
//        }
//
//
//        final var confirmedProductId = maybeValidatedPurchase
//                .map(ValidatedPurchase::productId)
//                .orElse(productId);
//
//        unsubscribeUserIfPurchasingRemoveAds(user, confirmedProductId);
//        createPurchase(confirmedProductId, user, platform, maybeValidatedPurchase);
//        return buildActivePurchasesDtos(confirmedProductId, user);
//    }


    /// Unfinished version that supports Android
    public IAPPurchaseValidationDTO validate(
            IAPReceiptDTO receipt,
            OSType platform,
            User user
    ) {
        final var productId = receipt.getProductId();
        final var receiptData = receipt.getToken();

        if (receiptData == null) {
            throw new RuntimeException("Invalid Purchase");
        }

        if (platform.equals(OSType.iOS)) {
            return validateIOSInAppPurchase(user, productId, receiptData);
        }

        if (platform.equals(OSType.Android)) {
            return validateAndroidInAppPurchase(user, receipt.getPurchaseId(), productId, receiptData);
        }

        return IAPPurchaseValidationDTO.builder().purchased(false).productId(productId).build();
    }

    private IAPPurchaseValidationDTO validateAndroidInAppPurchase(User user, String purchaseId, String productId, String receiptData) {
        final var userPurchase = userPurchasesRepository.findByUserAndProductId(user, productId);
        if (userPurchase != null) {
            return IAPPurchaseValidationDTO.builder().purchased(false).productId(productId).build();
        }

        final ValidatedPurchase validated;
        try {
            validated = googlePlayService.verifyPurchase(productId, purchaseId, receiptData);
        } catch (Exception e) {
            return IAPPurchaseValidationDTO.builder().purchased(false).productId(productId).build();
        }
        createPurchase(productId, user, OSType.Android, Optional.of(validated));
        return IAPPurchaseValidationDTO.builder().purchased(true).productId(productId).build();
    }


    private IAPPurchaseValidationDTO validateIOSInAppPurchase(User user, String productId, String receiptData) {
        try {
            Optional<ValidatedPurchase> maybeValidatedPurchase = appStoreConnectService.validateAppReceiptAndExtractTransactionId(productId, receiptData);

            if (maybeValidatedPurchase.isEmpty()) {
                throw new RuntimeException("Invalid Purchase");
            }

            final var confirmedProductId = maybeValidatedPurchase
                    .map(ValidatedPurchase::productId)
                    .orElse(productId);

            unsubscribeUserIfPurchasingRemoveAds(user, confirmedProductId);
            createPurchase(confirmedProductId, user, OSType.iOS, maybeValidatedPurchase);
            return buildActivePurchasesDtos(confirmedProductId, user);

        } catch (IOException | APIException | VerificationException e) {
            throw new RuntimeException(e);
        }
    }


    private void unsubscribeUserIfPurchasingRemoveAds(User user, String expectedProductId) {
        if (!expectedProductId.equals(yamlConfig.getRemoveAdsBundleId())) {
            return;
        }

        user.setUnsubscribed(true);
        userRepository.save(user);
    }

    private IAPPurchaseValidationDTO buildActivePurchasesDtos(String productId, User user) {
        final List<String> purchasedIds = userPurchasesRepository
                .findAllByUserAndStatus(user, UserPurchaseStatus.ACTIVE)
                .stream()
                .map(UserPurchase::getProductId)
                .collect(Collectors.toList());

        return IAPPurchaseValidationDTO
                .builder()
                .purchased(false)
                .productId(productId)
                .products(purchasedIds)
                .build();
    }

    private void createPurchase(String productId, User user, OSType platform, Optional<ValidatedPurchase> validatedPurchase) {
        if (productId == null) {
            return;
        }

        var purchase = userPurchasesRepository.findByUserAndProductId(user, productId);
        if (purchase == null) {
            purchase = new UserPurchase();
            purchase.setTimestamp(LocalDateTime.now());
        }


        purchase.setProductId(productId);
        purchase.setUser(user);

        StorePlatform storePlatform;
        switch (platform) {
            case iOS -> storePlatform = StorePlatform.APPLE;
            case Android -> storePlatform = StorePlatform.GOOGLE;
            default -> storePlatform = StorePlatform.ADMIN_ADDED;
        }

        purchase.setPlatform(storePlatform);
        purchase.setLastChecked(LocalDateTime.now());


        if (validatedPurchase.isPresent()) {
            var validated = validatedPurchase.get();
            purchase.setTransactionId(validated.transactionId());
            purchase.setProductType(validated.productType());
            purchase.setStatus(validated.status());
            purchase.setProductType(validated.productType());
            purchase.setAutoRenew(validated.autoRenew());

            var maybeExpiration = validated.expirationDate();
            if (maybeExpiration.isPresent()) {
                purchase.setExpirationDate(maybeExpiration.get());
            }

        } else {
            purchase.setStatus(UserPurchaseStatus.ACTIVE);
            purchase.setProductType(UserPurchaseProductType.NON_CONSUMABLE);
        }

        userPurchasesRepository.save(purchase);
    }

    public List<String> getPurchases(User user) {
        try {
            return userPurchasesRepository
                    .findAllByUserAndStatus(user, UserPurchaseStatus.ACTIVE)
                    .stream()
                    .map(UserPurchase::getProductId)
                    .toList();
        } catch (Exception e) {
            return List.of();
        }
    }

    public String checkStoreConnection(OSType platform) throws APIException, VerificationException, IOException, InterruptedException {
        switch (platform) {
            case iOS -> {
                try {
                    final var receipt = appStoreConnectService.checkAppStoreConnection();
                    return receipt.toString();
                } catch (APIException | IOException | InterruptedException | VerificationException e) {
                    throw e;
                }
            }
            case Android -> {
                return "Not Implemented";
            }
            case WEB -> {
                return "Not Supported";
            }
            default -> {
                return "Unknown Platform";
            }
        }
    }

    public void handleAppStoreNotification(String payload) throws VerificationException, IOException {
        var notification = appStoreConnectService.decodeServerNotification(payload);

        switch (notification.type()) {
            case EXPIRED, REFUND, REVOKE -> {
                cancelPurchase(notification);
            }
            case REFUND_REVERSED -> {
                restorePurchase(notification);
            }
            case DID_RENEW, DID_CHANGE_RENEWAL_PREF, DID_CHANGE_RENEWAL_STATUS -> {
                updatePurchaseInformation(notification);
            }

            case SUBSCRIBED -> {
                if (notification.subtype().equals(Subtype.RESUBSCRIBE)) {
                    updatePurchaseInformation(notification);
                }
            }
            default -> {
                // Do nothing
            }

//            case OFFER_REDEEMED -> {
//            }
//            case DID_FAIL_TO_RENEW -> {
//            }
//            case GRACE_PERIOD_EXPIRED -> {
//            }
//            case PRICE_INCREASE -> {
//            }
//            case REFUND_DECLINED -> {
//            }
//            case CONSUMPTION_REQUEST -> {
//            }
//            case RENEWAL_EXTENDED -> {
//            }
//            case TEST -> {
//            }
//            case RENEWAL_EXTENSION -> {
//            }
//            case EXTERNAL_PURCHASE_TOKEN -> {
//            }
//            case ONE_TIME_CHARGE -> {
//            }
        }
    }

    @Scheduled(cron = "0 0 0 * * ?") // Runs daily at midnight
    public void processExpiredPurchases() {
        List<UserPurchase> expiredPurchases = userPurchasesRepository.findExpiredPurchases();

        if (!expiredPurchases.isEmpty()) {
            expiredPurchases.forEach(purchase -> purchase.setStatus(UserPurchaseStatus.EXPIRED));
            userPurchasesRepository.saveAll(expiredPurchases);
        }
    }

    private void updatePurchaseInformation(AppStoreNotification notification) {
        findUserPurchase(notification).ifPresent(userPurchase ->
            notification.purchase()
                .flatMap(ValidatedPurchase::expirationDate)
                .ifPresent(expirationDate -> {

                    Boolean autoRenew = null;
                    UserPurchaseStatus status = UserPurchaseStatus.ACTIVE;

                    if (notification.subtype() != null) {
                        switch (notification.subtype()) {
                            case INITIAL_BUY, RESUBSCRIBE, DOWNGRADE, UPGRADE, AUTO_RENEW_ENABLED -> {
                                autoRenew = true;
                            }
                            case AUTO_RENEW_DISABLED -> {
                                autoRenew = false;
                            }
                            case FAILURE -> {
                                status = UserPurchaseStatus.EXPIRED;
                                autoRenew = false;
                            }
                        }
                    }

                    userPurchase.setExpirationDate(expirationDate);
                    userPurchase.setAutoRenew(autoRenew);
                    userPurchase.setStatus(status);
                    userPurchase.setProductId(notification.purchase().get().productId());
                    userPurchasesRepository.save(userPurchase);
                })
        );
    }

    private void restorePurchase(AppStoreNotification notification) {
        var maybePurchase = findUserPurchase(notification);
        maybePurchase.ifPresent(purchase -> {
            purchase.setStatus(UserPurchaseStatus.ACTIVE);
            purchase.setLastChecked(LocalDateTime.now());

            userPurchasesRepository.save(purchase);
        });

    }

    private void cancelPurchase(AppStoreNotification notification) {
        var maybePurchase = findUserPurchase(notification);

        maybePurchase.ifPresent(purchase -> {
            purchase.setStatus(notification.type().equals(NotificationTypeV2.REFUND)
                    ? UserPurchaseStatus.REFUNDED
                    : UserPurchaseStatus.EXPIRED);
            purchase.setLastChecked(LocalDateTime.now());
            userPurchasesRepository.save(purchase);
        });
    }

    private Optional<UserPurchase> findUserPurchase(AppStoreNotification notification) {
        return notification.purchase().flatMap(purchase -> {
            var id = purchase.transactionId();
            return userPurchasesRepository.findByTransactionId(id);
        });
    }

    @SneakyThrows
    public void getNotificationHistory() {
        appStoreConnectService.investigateNotificationHistory();
    }


    public void handleGooglePlayNotification(GoogleNotification.DeveloperNotification message) {
        message.oneTimeProductNotification().ifPresent(this::handleOneTimeProductNotification);
        message.subscriptionNotification().ifPresent(this::handleSubscriptionNotification);
        message.voidedPurchaseNotification().ifPresent(this::handleVoidedPurchaseNotification);
    }

    private void handleVoidedPurchaseNotification(GoogleNotification.VoidedPurchaseNotification voidedPurchaseNotification) {
        final var orderId = voidedPurchaseNotification.orderId();
        final var userPurchase = userPurchasesRepository.findByTransactionId(orderId);

        userPurchase.ifPresent(purchase -> {
            purchase.setStatus(UserPurchaseStatus.REFUNDED);
            userPurchasesRepository.save(purchase);
        });
    }

    private void handleSubscriptionNotification(GoogleNotification.SubscriptionNotification subscriptionNotification) {
        System.out.println(subscriptionNotification);
    }

    private void handleOneTimeProductNotification(GoogleNotification.OneTimeProductNotification oneTimeProductNotification) {
        System.out.println(oneTimeProductNotification);
    }
}
