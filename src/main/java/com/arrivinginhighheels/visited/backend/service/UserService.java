package com.arrivinginhighheels.visited.backend.service;

import com.arrivinginhighheels.visited.backend.config.Constants.Counters;
import com.arrivinginhighheels.visited.backend.dto.UserChangePasswordRequest;
import com.arrivinginhighheels.visited.backend.exception.EmailAlreadyExistsException;
import com.arrivinginhighheels.visited.backend.exception.InsufficientParametersException;
import com.arrivinginhighheels.visited.backend.exception.UserNotFoundByUsernameException;
import com.arrivinginhighheels.visited.backend.features.authentication.AuthRequest;
import com.arrivinginhighheels.visited.backend.features.cities.UserCitiesRepository;
import com.arrivinginhighheels.visited.backend.features.experiences.UserExperienceAreaRepository;
import com.arrivinginhighheels.visited.backend.features.iap.UserPurchasesRepository;
import com.arrivinginhighheels.visited.backend.features.inspirations.UserInspirationsRepository;
import com.arrivinginhighheels.visited.backend.features.itineraries.UserItineraryRepository;
import com.arrivinginhighheels.visited.backend.features.poster.PrintingRepository;
import com.arrivinginhighheels.visited.backend.features.privacy.PrivacyService;
import com.arrivinginhighheels.visited.backend.features.todoLists.UserTodoListItemRepository;
import com.arrivinginhighheels.visited.backend.model.Counter;
import com.arrivinginhighheels.visited.backend.model.User;
import com.arrivinginhighheels.visited.backend.model.UserDeletionQueue;
import com.arrivinginhighheels.visited.backend.repository.*;
import com.arrivinginhighheels.visited.backend.security.utils.UserBuilder;
import jakarta.validation.Valid;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.logging.Logger;

/**
 * Business logic class for the users
 */
@Service
public class UserService {

    private static final Logger log = Logger.getLogger("UserService");

    private final UserRepository userRepository;

    private final UserBuilder userBuilder;

    private final CounterRepository counterRepository;

    private final PrivacyService privacyService;

    private final SelectionRepository selectionRepository;

    private final UserBeenCountRepository userBeenCountRepository;

    private final UserDeletionQueueRepository userDeletionQueueRepository;

    private final SessionRepository sessionRepository;

    private final GeoAreaUserNotesRepository geoAreaUserNotesRepository;

    private final UserExperienceAreaRepository userExperienceAreaRepository;

    private final UserScoresRepository userScoresRepository;

    private final UserPurchasesRepository userPurchasesRepository;

    private final UserCitiesRepository userCitiesRepository;

    private final UserInspirationsRepository userInspirationsRepository;

    private final UserTodoListItemRepository userTodoListItemRepository;

    private final PrintingRepository printingRepository;

    private final PasswordEncoder passwordEncoder;

    private final UserItineraryRepository userItineraryRepository;

    public UserService(
            UserDeletionQueueRepository userDeletionQueueRepository,
            UserRepository userRepository,
            UserBuilder userBuilder,
            UserScoresRepository userScoresRepository,
            UserInspirationsRepository userInspirationsRepository,
            CounterRepository counterRepository,
            PrivacyService privacyService,
            SelectionRepository selectionRepository,
            UserBeenCountRepository userBeenCountRepository,
            SessionRepository sessionRepository,
            PasswordEncoder passwordEncoder,
            GeoAreaUserNotesRepository geoAreaUserNotesRepository,
            UserTodoListItemRepository userTodoListItemRepository,
            PrintingRepository printingRepository,
            UserPurchasesRepository userPurchasesRepository,
            UserCitiesRepository userCitiesRepository,
            UserExperienceAreaRepository userExperienceAreaRepository,
            UserItineraryRepository userItineraryRepository) {
        this.userDeletionQueueRepository = userDeletionQueueRepository;
        this.userRepository = userRepository;
        this.userBuilder = userBuilder;
        this.userScoresRepository = userScoresRepository;
        this.userInspirationsRepository = userInspirationsRepository;
        this.counterRepository = counterRepository;
        this.privacyService = privacyService;
        this.selectionRepository = selectionRepository;
        this.userBeenCountRepository = userBeenCountRepository;
        this.sessionRepository = sessionRepository;
        this.passwordEncoder = passwordEncoder;
        this.geoAreaUserNotesRepository = geoAreaUserNotesRepository;
        this.userTodoListItemRepository = userTodoListItemRepository;
        this.printingRepository = printingRepository;
        this.userPurchasesRepository = userPurchasesRepository;
        this.userCitiesRepository = userCitiesRepository;
        this.userExperienceAreaRepository = userExperienceAreaRepository;
        this.userItineraryRepository = userItineraryRepository;
    }

    @Transactional
    public User createUser(final AuthRequest userDTO) {

        var email = userDTO.getEmail();
        if (email != null) {
            email = email.toLowerCase();
        }

        if (userRepository.findByUsername(email).isPresent()) {
            throw new EmailAlreadyExistsException();
        }

        final Counter totalUsersCounter = getTotalUsersCounter();
        final long nextTotalUsers = totalUsersCounter.getValue() + 1;

        final var user = userBuilder.createUserFromDTO(userDTO);
        user.setRanking(nextTotalUsers);
        user.setNumberOfAreasVisited(0);
        user.setCreationDate(new Date());

        final var userSaved = userRepository.save(user);

        totalUsersCounter.setValue(nextTotalUsers);
        counterRepository.save(totalUsersCounter);

        return userSaved;
    }

    public void updatePassword(User user, UserChangePasswordRequest request) {
        //TODO: figure out how to correctly compare passwords
//        var encodedOldPassword = passwordEncoder.encode(request.getOldPassword().toLowerCase());
//        if (!user.getPassword().equals(encodedOldPassword)) {
//            throw new RuntimeException("Incorrect original password");
//        }

        var encoded = passwordEncoder.encode(request.getNewPassword());
        user.setPassword(encoded);
        userRepository.save(user);
    }

    public User findUser(final String username) {
        if (username == null) {
            throw new InsufficientParametersException("id");
        }


        return userRepository.findByUsername(username)
                .orElseThrow(() -> new UserNotFoundByUsernameException(username));
    }

    @Transactional
    public void recalculateUsersTotalNumberOfAreasVisited(final User user) {
        final Integer totalNumberOfAreasVisited = userRepository.getTotalNumberOfAreasVisitedForTheUser(user.getId());

        user.setNumberOfAreasVisited(totalNumberOfAreasVisited);

        log.info("Setting the total number of areas visited for user " + user.getUsername() + " to " +
                totalNumberOfAreasVisited);

        userRepository.saveAndFlush(user);
    }

    @Transactional
    public Long calculateRankForTheUser(final User user) {
        log.info("Calculating the ranking for the user " + user.getUsername());
        final Long rankForTheUser = userRepository.getRankForTheUser(user.getId());
        user.setRanking(rankForTheUser);
        userRepository.save(user);

        return user.getRanking();
    }

    @Transactional
    public Long countUsers() {
        final Counter totalUsersCounter = getTotalUsersCounter();

        return totalUsersCounter.getValue();
    }

    private Counter getTotalUsersCounter() {
        Counter totalUsersCounter = counterRepository.findByName(Counters.TOTAL_USERS);
        if (totalUsersCounter == null) {
            final Long totalUsers = userRepository.count();
            totalUsersCounter = new Counter(Counters.TOTAL_USERS, totalUsers);
            counterRepository.save(totalUsersCounter);
        }

        return totalUsersCounter;
    }

    @Transactional
    public void deleteUser(final User user) {

        //remove all data relative to the user in the database
        userExperienceAreaRepository.deleteByUser(user);
        userBeenCountRepository.deleteByUser(user);
        userScoresRepository.deleteByUserId(user.getId());
        userPurchasesRepository.deleteByUser(user);
        userTodoListItemRepository.deleteByUser(user);
        userCitiesRepository.deleteByUser(user);
        userInspirationsRepository.deleteByUser(user);
        privacyService.deletePrivacyAgreementForUserId(user);
        printingRepository.deleteByUser(user);
        userItineraryRepository.deleteAllByUser(user);

        try {
            selectionRepository.deleteByUserId(user.getId());
        } catch (Exception ignored) {
        }

        try {
            sessionRepository.deleteByUserId(user.getId());
        } catch (Exception ignored) {
        }

        try {
            geoAreaUserNotesRepository.deleteByUserId(user.getId());
        } catch (Exception ignored) {
        }

        //save the user deletion in the queue to remove dirty data from the Selection Logs later
        final UserDeletionQueue userDeletionQueue = new UserDeletionQueue(user.getId());
        userDeletionQueueRepository.save(userDeletionQueue);

        //And finally
        userRepository.delete(user);

        //update counters
        updateUserCounter();

        log.info("Deleted user " + user.getUsername());
    }

    private void updateUserCounter() {
        Counter totalUsersCounter = counterRepository.findByName(Counters.TOTAL_USERS);
        final Long totalUsers = userRepository.count();

        // Unlikely for this to happen, but you never know...
        if (totalUsersCounter == null) {
            totalUsersCounter = new Counter(Counters.TOTAL_USERS, totalUsers);
        }

        totalUsersCounter.setValue(totalUsers);
        counterRepository.save(totalUsersCounter);
    }

    public void sendPasswordResetRequest(@Valid String email) {

    }
}
